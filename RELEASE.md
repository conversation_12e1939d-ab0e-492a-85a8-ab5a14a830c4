# RELEASE

- [RELEASE](#release)
  - [Setup MC](#setup-mc)
  - [Note](#note)
  - [Tạo ảnh khởi động ứng dụng bootsplash\_logo.png](#tạo-ảnh-khởi-động-ứng-dụng-bootsplash_logopng)
    - [1. <PERSON><PERSON><PERSON> bị ảnh khởi động](#1-chuẩn-bị-ảnh-khởi-động)
    - [2. Tạo ảnh khởi động từ ảnh đã chuẩn bị](#2-tạo-ảnh-khởi-động-từ-ảnh-đã-chuẩn-bị)
  - [Release ứng dụng](#release-ứng-dụng)
    - [1. Trình tự release 1 mini app](#1-trình-tự-release-1-mini-app)
    - [2. Cách tag Mini App](#2-cách-tag-mini-app)
    - [3. Push tag](#3-push-tag)
    - [4. Xóa bỏ tag nếu nhỡ](#4-xóa-bỏ-tag-nếu-nhỡ)
    - [4. T<PERSON>ế<PERSON>](#4-tiếp)
  - [Release iOS](#release-ios)
    - [Note install ios](#note-install-ios)
    - [Bước 1](#bước-1)
    - [Bước 2](#bước-2)
  - [Release android](#release-android)
    - [Build AAB](#build-aab)
    - [Release to firebase](#release-to-firebase)
      - [Release android to firebase](#release-android-to-firebase)
      - [Release iOS to firebase](#release-ios-to-firebase)
    - [Install AAB to device](#install-aab-to-device)
- [this commit is to know that the app is running before apply firebase](#this-commit-is-to-know-that-the-app-is-running-before-apply-firebase)

## Setup MC

```sh
pnpm node scripts/build-miniapp.js dashboard-ios@0.0.1
```

## Note

Xem Github Page: [https://dl-storage.uat.lokify.xplat.online/ihanoi-staff/](https://dl-storage.uat.lokify.xplat.online/ihanoi-staff/)

## Tạo ảnh khởi động ứng dụng bootsplash_logo.png

### 1. Chuẩn bị ảnh khởi động

Chuẩn bị ảnh khởi động ứng dụng `bootsplash_logo.png`. Yêu cầu ảnh nền trong suốt và di chuyển nó vào `packages/hosts/assets`

### 2. Tạo ảnh khởi động từ ảnh đã chuẩn bị

```sh
cd packages/host
pnpm react-native generate-bootsplash assets/bootsplash_logo.png  --logo-width=--logo-width=192
```

## Release ứng dụng

Hiện tại ta có 2 ứng dụng con là

- auth
- dashboard

### 1. Trình tự release 1 mini app

LƯU Ý CHỈ TAG 1 MINI APP VÀ DEPLOY, SAU ĐÓ LẶP LẠI TƯƠNG TỰ NẾU CÓ VỚI MINI APP TIẾP THEO (VÌ GITHUB PAGES CHỈ RUN 1 ACTION 1 LÚC THÔI)

1. Nâng version trong package.json của mini app tương ứng (Ví dụ 0.0.1) +update file compatibility-matrix.json
2. Tag version mini app đúng với version trong package.json vửa sửa
3. Push tag lên
4. Chờ deploy mini app
5. Tiếp tục release mini app khác theo tuần tự
6. Build lại catalog để update cache mini app và deploy lại trên K8s
7. pnpm node scripts/update-compatibility-matrix.js

### 2. Cách tag Mini App

Cách tag: git tag `{auth|dashboard}`-`{ios|android}`@`{version}`

Ví dụ:

```sh
# Android
git tag dashboard-ios@0.0.1
pnpm node scripts/build-miniapp.js dashboard-ios@0.0.1

git tag dashboard-android@0.0.1
pnpm node scripts/build-miniapp.js dashboard-android@0.0.1


# Auth
git tag auth-ios@0.0.1
pnpm node scripts/build-miniapp.js auth-ios@0.0.1

git tag auth-android@0.0.1
pnpm node scripts/build-miniapp.js auth-android@0.0.1
```

### 3. Push tag

```sh
git push origin dashboard-ios@0.0.1
git push origin auth-ios@0.0.1
git push origin dashboard-android@0.0.1
git push origin auth-android@0.0.1
```

### 4. Xóa bỏ tag nếu nhỡ

```sh
git tag -d dashboard-ios@0.0.1
git tag -d auth-ios@0.0.1
git tag -d dashboard-android@0.0.1
git tag -d auth-android@0.0.1
```

```sh
git tag --delete dashboard-ios@0.0.1
git push --delete origin dashboard-ios@0.0.1
git tag --delete auth-ios@0.0.1
git push --delete origin auth-ios@0.0.1
```

Sau đó chờ GitHub Action chạy, xong thì tiếp tục release mini app tiếp theo nếu có

### 4. Tiếp

Tiếp tục bước 5 và 6 sau khi build đủ mini app lên GitHub pages

## Release iOS

### Note install ios

If build error run: `sudo ln -s $(command -v node) /usr/local/bin/node`

### Bước 1

```sh
pnpm copy:prod
pnpm bundle:ios
```

### Bước 2

Mở XCode

1. Nhớ nâng version (Ảnh: [./images/inc-version-build.png](./images/inc-version-build.png) nâng từ build 16/ version 1.6 lên build 18/ version 1.8 chẳng hạn)
1. Product -> Destination -> Archive Any iOS Device (arm64)
1. Product -> Archive

Lưu ý: Nếu lỗi thì chạy `ln -s $(command -v node) /usr/local/bin/node`. Thêm sudo vào nếu cần

- Nếu bị lỗi `hermes-engine` thì chỉnh lại `hermes-engine` trong file `with-environment.sh`

## Release android

### Build AAB

```sh
# Export biến môi trường
export NODE_ENV=production
export SAS_CATALOG_SERVER_URL=https://catalog.demo.pro
# export SAS_CATALOG_SERVER_URL=https://super-app-showcase-catalog.vercel.app
export ST_SSO_AUTHORIZATION_ENDPOINT='https://auth.demo.pro/realms/demo/protocol/openid-connect/auth'
export ST_SSO_CLIENT_ID='demo'
export ST_SSO_CLIENT_SECRET='demo'
export ST_SSO_REALM='demo'
export ST_SSO_SCOPES='openid'
export ST_SSO_TOKEN_ENDPOINT='https://auth.demo.pro/realms/demo/protocol/openid-connect/token'
export ST_API_ENDPOINT='https://api.demo.pro/api'


# export ANDROID_HOME=$HOME/Library/Android/sdk
# export PATH=$PATH:$ANDROID_HOME/emulator
# export PATH=$PATH:$ANDROID_HOME/platform-tools

# Copy env production
pnpm copy:prod
# Bundle
pnpm bundle:qn:host:android
# Build Production
pnpm build:android:prod
# Install app
./scripts/abb-to-apks
```

### Release to firebase

Note: Config `FIREBASE_TOKEN` in your `.zshrc` or `.bashrc`

Install firebase tools

```sh
npm install -g firebase-tools
firebase login --reauth
```

#### Release android to firebase

```sh
./scripts/deploy-android
```

#### Release iOS to firebase

```sh
./scripts/deploy-ios
```

### Install AAB to device

Download tools

```sh
wget https://github.com/google/bundletool/releases/download/1.17.1/bundletool-all-1.17.1.jar
```

Note: Cài đặt lên điện thoại (Nhớ cắm máy vô hoặc bật máy ảo trước)

```sh
./scripts/abb-to-apks
```

<!--
fpt is company limited
fis.gs.congchuc

fpt technology
vn.gov.hanoi.dichvucong.congchuc -->

<!--  recommend Chạy local các mini app check env truoc khi build mini app -->
<!-- const containersLocal = { "auth": "http://localhost:9003/[name][ext]", "dashboard": "http://localhost:9002/[name][ext]" } -->
<!-- sau khi run check qua dung env thi hoan toan co the chay bundle va ac-app-store-cli -->
<!-- check miniapp version truoc khi build  -->

<!-- cau truc build mini app -->

<!--
pnpm --filter [miniapp package folder] switch:client [client name : HN || QN]
pnpm --filter auth copy:env [env name : prod || uat || dev]
pnpm --filter auth bundle:android || pnpm --filter auth bundle:ios
-->

<!--vi du khi build mini app -->

```sh
# Build auth HN prod
pnpm --filter auth switch:client HN
pnpm --filter auth copy:env prod
pnpm --filter auth bundle:android
pnpm --filter auth bundle:ios
pnpm ac-app-store-cli --configFile=ac-store-congchuc-hno-auth.json
# Build dashboard HN prod
pnpm --filter dashboard switch:client HN
pnpm --filter dashboard copy:env prod
pnpm --filter dashboard bundle:android
pnpm --filter dashboard bundle:ios
pnpm ac-app-store-cli --configFile=ac-store-congchuc-hno-dashboard.json
# Build host HN
pnpm copy:prod
pnpm bundle:hn:host:android
# Build Production
pnpm build:android:prod
# Install app
./scripts/abb-to-apks
```

```sh
# Build citizen QN
pnpm --filter citizen switch:client QN
pnpm --filter citizen copy:env prod
pnpm --filter citizen bundle:android
pnpm --filter citizen bundle:ios
pnpm ac-app-store-cli --configFile=ac-store-congchuc-qnh-citizen.json
# Build auth QN
pnpm --filter auth switch:client QN
pnpm --filter auth copy:env prod
pnpm --filter auth bundle:android
pnpm --filter auth bundle:ios
pnpm ac-app-store-cli --configFile=ac-store-congchuc-qnh-auth.json
# Build dashboard QN
pnpm --filter dashboard switch:client QN
pnpm --filter dashboard copy:env prod
pnpm --filter dashboard bundle:android
pnpm --filter dashboard bundle:ios
pnpm ac-app-store-cli --configFile=ac-store-congchuc-qnh-dashboard.json
# Build host QN
pnpm copy:prod
pnpm bundle:qn:host:android
# Build Production
pnpm build:android:prod

# Build host QNC
pnpm copy:prod
pnpm bundle:qnc:host:android
# Build Production
pnpm build:android:prod
# Install app
./scripts/abb-to-apks
```

# this commit is to know that the app is running before apply firebase
