import * as React from 'react';
import Svg, {SvgProps, Path, G} from 'react-native-svg';

export const HandleDoc = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <G clipPath="url(#clip0_642_6380)">
      <Path
        fill={props.color || '#366AE2'}
        d="M21.715 6.98c-.85-.94-2.27-1.41-4.33-1.41h-.24v-.04c0-1.68 0-3.76-3.76-3.76h-1.52c-3.76 0-3.76 2.09-3.76 3.76v.05h-.24c-2.07 0-3.48.47-4.33 1.41-.99 1.1-.96 2.58-.86 3.59l.01.07.077.813c.015.15.095.285.221.368.24.157.641.415.882.549.***********.44.25 1.71.94 3.59 1.57 5.5 **********.5 2.04 2.69 2.04s2.62-1.09 2.69-2.06c2.04-.33 4.01-1.04 5.79-2.08.06-.03.1-.06.15-.09.397-.224.808-.5 1.183-.771a.493.493 0 0 0 .201-.346l.016-.143.05-.47c.01-.06.01-.11.02-.18.08-1.01.06-2.39-.88-3.43Zm-8 6.85c0 1.06 0 1.22-1.23 1.22s-1.23-.19-1.23-1.21v-1.26h2.46v1.25Zm-4.18-8.26v-.04c0-1.7 0-2.33 2.33-2.33h1.52c2.33 0 2.33.64 2.33 2.33v.05h-6.18v-.01Z"
      />
      <Path
        fill={props.color || '#366AE2'}
        d="M21.498 13.734a.509.509 0 0 1 .726.502l-.359 3.954c-.21 2-1.03 4.04-5.43 4.04h-7.62c-4.4 0-5.22-2.04-5.43-4.03l-.34-3.748a.508.508 0 0 1 .715-.505c1.14.515 3.242 1.43 4.542 1.77a.57.57 0 0 1 .369.314c.607 1.299 1.923 1.99 3.824 1.99 1.882 0 3.215-.718 3.824-2.02a.572.572 0 0 1 .37-.314c1.38-.363 3.618-1.385 4.81-1.953Z"
      />
    </G>
  </Svg>
);
