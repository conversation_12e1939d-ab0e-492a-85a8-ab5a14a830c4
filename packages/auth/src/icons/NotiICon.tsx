import * as React from 'react';
import Svg, {
  SvgProps,
  Path,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';
export const NotiIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path fill="url(#a)" d="M19.965 8.923a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
    <Path
      fill="url(#b)"
      d="M19.965 10.424c-2.48 0-4.5-2.02-4.5-4.5 0-.72.19-1.39.49-2h-7.47c-3.45 0-5.52 2.06-5.52 5.52v7.95c0 3.47 2.07 5.53 5.52 5.53h7.95c3.46 0 5.52-2.06 5.52-5.52v-7.47c-.6.3-1.27.49-1.99.49Z"
      opacity={0.4}
    />
    <Path
      fill="url(#c)"
      d="M12.715 14.924h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z"
    />
    <Path
      fill="url(#d)"
      d="M16.715 18.924h-9c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h9c.41 0 .75.34.75.75s-.34.75-.75.75Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={16.965}
        x2={22.965}
        y1={2.923}
        y2={8.923}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
      <LinearGradient
        id="b"
        x1={2.965}
        x2={21.965}
        y1={3.923}
        y2={22.913}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
      <LinearGradient
        id="c"
        x1={6.965}
        x2={7.623}
        y1={13.424}
        y2={16.272}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
      <LinearGradient
        id="d"
        x1={6.965}
        x2={7.385}
        y1={17.424}
        y2={20.363}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
    </Defs>
  </Svg>
);
