import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';
export const ArchiveBookActiveIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path
      fill={props.color || '#366AE2'}
      d="M12.442 2.083V7c0 .367-.434.55-.7.308l-1.459-1.341a.413.413 0 0 0-.566 0L8.258 7.3a.416.416 0 0 1-.7-.3V2.083c0-.233.184-.416.417-.416h4.05c.233 0 .417.183.417.416Z"
    />
    <Path
      fill={props.color || '#366AE2'}
      d="M14.15 1.717c-.242-.034-.458.175-.458.416V7.15c0 .633-.375 1.208-.959 1.467a1.602 1.602 0 0 1-1.725-.292l-.725-.667a.413.413 0 0 0-.566 0l-.725.667c-.3.283-.692.425-1.084.425-.216 0-.433-.042-.641-.133a1.605 1.605 0 0 1-.959-1.467V2.133c0-.241-.216-.45-.458-.416-2.333.291-3.35 1.866-3.35 4.116v8.334c0 2.5 1.25 4.166 4.167 4.166h6.666c2.917 0 4.167-1.666 4.167-4.166V5.833c0-2.25-1.017-3.825-3.35-4.116Zm.433 13.908H7.5A.63.63 0 0 1 6.875 15a.63.63 0 0 1 .625-.625h7.083a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm0-3.333h-3.541a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h3.541a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Z"
    />
  </Svg>
);
