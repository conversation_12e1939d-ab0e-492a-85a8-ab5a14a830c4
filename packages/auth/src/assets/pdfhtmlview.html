
<!DOCTYPE html>
<html>
<header>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

</header>

<body>
     
    <style>
      .hide{
            display: none;
        }
        .signatureRect {
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            width: 100px;
            height: 50px;
            cursor: move;
            background: rgba(0, 0, 0, 0.3);


        }
        .pageging{
            font-size: 15px;
        }
    
        @media (max-width: 600px) {

            #addSignatureBtn,
            #submitBtn {
                width: 100%;
            }
        }

        *,
        ::after,
        ::before {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            font-family: var(--bs-body-font-family);
            font-size: var(--bs-body-font-size);
            font-weight: var(--bs-body-font-weight);
            line-height: var(--bs-body-line-height);
            color: var(--bs-body-color);
            text-align: var(--bs-body-text-align);
            background-color: var(--bs-body-bg);
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
            margin-bottom: 60px;
            font-family: 'Roboto';
            font-size: 40px;
        }

        ul {
            padding-left: 2rem;
            margin-top: 0;
            margin-bottom: 1rem;
        }

        a {
            color: #0d6efd;
            text-decoration: underline;
        }

        a:hover {
            color: #0a58ca;
        }

        label {
            display: inline-block;
        }

        button {
            border-radius: 0;
            margin: 0;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            text-transform: none;
            border-radius: .25rem;
        }

        button:focus:not(:focus-visible) {
            outline: 0;
        }

        [type=button],
        button {
            -webkit-appearance: button;
        }

        [type=button]:not(:disabled),
        button:not(:disabled) {
            cursor: pointer;
        }

        .container,
        .container-fluid {
            width: 100%;
            padding-right: var(--bs-gutter-x, .75rem);
            padding-left: var(--bs-gutter-x, .75rem);
            margin-right: auto;
            margin-left: auto;
        }

        @media (min-width: 576px) {
            .container {
                max-width: 540px;
            }
        }

        @media (min-width: 768px) {
            .container {
                max-width: 720px;
            }
        }

        @media (min-width: 992px) {
            .container {
                max-width: 960px;
            }

            .col-lg-6 {
                flex: 0 0 auto;
                width: 50%;
            }

            .offset-lg-2 {
                margin-left: 16.66666667%;
            }
        }

        @media (min-width: 1200px) {
            .container {
                max-width: 1140px;
            }
        }

        @media (min-width: 1400px) {
            .container {
                max-width: 1320px;
            }
        }

        .row {
            --bs-gutter-x: 1.5rem;
            --bs-gutter-y: 0;
            display: flex;
            flex-wrap: wrap;
            margin-top: calc(var(--bs-gutter-y) * -1);
            margin-right: calc(var(--bs-gutter-x) * -.5);
            margin-left: calc(var(--bs-gutter-x) * -.5);
        }

        .row>* {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
            padding-right: calc(var(--bs-gutter-x) * .5);
            padding-left: calc(var(--bs-gutter-x) * .5);
            margin-top: var(--bs-gutter-y);
        }

        .btn {
            display: inline-block;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            text-align: center;
            text-decoration: none;
            vertical-align: middle;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
            background-color: transparent;
            border: 1px solid transparent;
            padding: .375rem .75rem;
            font-size: 1rem;
            border-radius: .25rem;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        }

        @media (prefers-reduced-motion: reduce) {
            .btn {
                transition: none;
            }
        }

        .btn:hover {
            color: #212529;
        }

        .btn:focus {
            outline: 0;
            box-shadow: 0 0 0 .25rem rgba(13, 110, 253, .25);
        }

        .btn:disabled {
            pointer-events: none;
            opacity: .65;
        }

        .btn-primary {
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            color: #fff;
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }

        .btn-primary:focus,
        .btn:focus,
        .btn:active:focus {
            color: #fff;
            background-color: #0b5ed7;
            border-color: #0a58ca;
            box-shadow: 0 0 0 .25rem rgba(49, 132, 253, .5), 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
        }

        .btn-primary:active {
            color: #fff;
            background-color: #0a58ca;
            border-color: #0a53be;
        }

        .btn-primary:active:focus {
            box-shadow: 0 0 0 .25rem rgba(49, 132, 253, .5);
        }

        .btn-primary:disabled {
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-success {
            color: #fff;
            background-color: #198754;
            border-color: #198754;
        }

        .btn-success:hover {
            color: #fff;
            background-color: #157347;
            border-color: #146c43;
        }

        .btn-success:focus {
            color: #fff;
            background-color: #157347;
            border-color: #146c43;
            box-shadow: 0 0 0 .25rem rgba(60, 153, 110, .5);
        }

        .btn-success:active {
            color: #fff;
            background-color: #146c43;
            border-color: #13653f;
        }

        .btn-success:active:focus {
            box-shadow: 0 0 0 .25rem rgba(60, 153, 110, .5);
        }

        .btn-success:disabled {
            color: #fff;
            background-color: #198754;
            border-color: #198754;
        }

        .fade {
            transition: opacity .15s linear;
        }

        @media (prefers-reduced-motion: reduce) {
            .fade {
                transition: none;
            }
        }

        .fade:not(.show) {
            opacity: 0;
        }

        .collapse:not(.show) {
            display: none;
        }

        .nav-link {
            display: block;
            padding: .5rem 1rem;
            color: #0d6efd;
            text-decoration: none;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out;
        }

        @media (prefers-reduced-motion: reduce) {
            .nav-link {
                transition: none;
            }
        }

        .nav-link:focus,
        .nav-link:hover {
            color: #0a58ca;
        }

        .navbar {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;
            padding-top: .5rem;
            padding-bottom: .5rem;
        }

        .navbar>.container-fluid {
            display: flex;
            flex-wrap: inherit;
            align-items: center;
            justify-content: space-between;
        }

        .navbar-brand {
            padding-top: .3125rem;
            padding-bottom: .3125rem;
            margin-right: 1rem;
            font-size: 1.25rem;
            text-decoration: none;
            white-space: nowrap;
        }

        .navbar-nav {
            display: flex;
            flex-direction: column;
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        .navbar-nav .nav-link {
            padding-right: 0;
            padding-left: 0;
        }

        .navbar-collapse {
            flex-basis: 100%;
            flex-grow: 1;
            align-items: center;
        }

        .navbar-toggler {
            padding: .25rem .75rem;
            font-size: 1.25rem;
            line-height: 1;
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: .25rem;
            transition: box-shadow .15s ease-in-out;
        }

        @media (prefers-reduced-motion: reduce) {
            .navbar-toggler {
                transition: none;
            }
        }

        .navbar-toggler:hover {
            text-decoration: none;
        }

        .navbar-toggler:focus {
            text-decoration: none;
            outline: 0;
            box-shadow: 0 0 0 .25rem;
        }

        .navbar-toggler-icon {
            display: inline-block;
            width: 1.5em;
            height: 1.5em;
            vertical-align: middle;
            background-repeat: no-repeat;
            background-position: center;
            background-size: 100%;
        }

        @media (min-width: 576px) {
            .navbar-expand-sm {
                flex-wrap: nowrap;
                justify-content: flex-start;
            }

            .navbar-expand-sm .navbar-nav {
                flex-direction: row;
            }

            .navbar-expand-sm .navbar-nav .nav-link {
                padding-right: .5rem;
                padding-left: .5rem;
            }

            .navbar-expand-sm .navbar-collapse {
                display: flex !important;
                flex-basis: auto;
            }

            .navbar-expand-sm .navbar-toggler {
                display: none;
            }

            .d-sm-inline-flex {
                display: inline-flex !important;
            }
        }

        .navbar-light .navbar-brand {
            color: rgba(0, 0, 0, .9);
        }

        .navbar-light .navbar-brand:focus,
        .navbar-light .navbar-brand:hover {
            color: rgba(0, 0, 0, .9);
        }

        .navbar-light .navbar-nav .nav-link {
            color: rgba(0, 0, 0, .55);
        }

        .navbar-light .navbar-nav .nav-link:focus,
        .navbar-light .navbar-nav .nav-link:hover {
            color: rgba(0, 0, 0, .7);
        }

        .navbar-light .navbar-toggler {
            color: rgba(0, 0, 0, .55);
            border-color: rgba(0, 0, 0, .1);
        }

        .navbar-light .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        .alert-dismissible {
            padding-right: 3rem;
        }

        .alert-success {
            color: #0f5132;
            background-color: #d1e7dd;
            border-color: #badbcc;
        }

        .d-inline-block {
            display: inline-block !important;
        }

        .d-flex {
            display: flex !important;
        }

        .border {
            border: 1px solid #dee2e6 !important;
        }

        .border-bottom {
            border-bottom: 1px solid #dee2e6 !important;
        }

        .w-100 {
            width: 100% !important;
        }

        .flex-grow-1 {
            flex-grow: 1 !important;
        }

        .justify-content-between {
            justify-content: space-between !important;
        }

        .mx-3 {
            margin-right: 1rem !important;
            margin-left: 1rem !important;
        }

        .mt-3 {
            margin-top: 1rem !important;
        }

        .mb-2 {
            margin-bottom: .5rem !important;
        }

        .mb-3 {
            margin-bottom: 1rem !important;
        }

        .pb-3 {
            padding-bottom: 1rem !important;
        }

        .text-dark {
            --bs-text-opacity: 1;
            color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
        }

        .bg-white {
            --bs-bg-opacity: 1;
            background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
        }

        .text-right {
            text-align: right;
        }

        #pdfContainer {
            position: relative;
            width: 100%;

            overflow: hidden;
        }

        #pdfCanvas {
            width: 100%;
            height: 100%;
        }

        #thumbnails {
            display: flex;
            gap: 5px;
            overflow-x: auto;
            margin-top: 10px;
            border: 1px solid #ccc;
            padding: 5px;
        }

        .thumbnail {
            cursor: pointer;
            width: 80px;
            border: 1px solid #ddd;
            transition: border 0.3s;
        }

        .thumbnail:hover {
            border: 1px solid #000;
        }

        .thumbnail.active {
            border: 2px solid blue;
        }

        .border-bottom[b-0n909ngonu] {
            border-bottom: 1px solid #e5e5e5;
        }

        .box-shadow[b-0n909ngonu] {
            box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
        }
    </style>
    <link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>
    <div class="main hide">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-6">
                <button id="addSignatureBtn" class="btn btn-success w-100">Chọn vị trí ký</button>
                <button id="submitBtn" class="btn btn-primary w100">Xác nhận</button>
            </div>
        </div>
    </div>
    <div class="container-fluid">
        <div class="row">
            <!-- PDF viewer container -->
            <div class="offset-lg-2 col-lg-6">
                <div class="border">
                    <div id="pdfContainer">
                        <canvas id="pdfCanvas" class="w-100"></canvas>
                    </div>
                </div>
                <!-- Thumbnail previews (if any) -->
                <div id="thumbnails" class="mt-3">
                    <!-- Thumbnail previews will be inserted here -->
                </div>
                <div class="text-right mb-3 mt-3 pageging">
                    <button id="prevBtn" class="btn btn-primary">Trước</button>
                    <div id="pageInfo" class="mx-3 d-inline-block">Trang: 1 / 1</div>
                    <button id="nextBtn" class="btn btn-primary">Next</button>
                </div>
            </div>
        </div>
    </div>
</div>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script type="module">
 
       const urlParams = new URLSearchParams(window.location.search);
        import * as pdfjsLib from "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.10.38/pdf.min.mjs";

        // Cấu hình workerSrc
        pdfjsLib.GlobalWorkerOptions.workerSrc = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.10.38/pdf.worker.min.mjs";

        const pdfContainer = document.getElementById("pdfContainer");
        const pdfCanvas = document.getElementById("pdfCanvas");
        let signatureRect = document.getElementById("signatureRect");
        const prevBtn = document.getElementById("prevBtn");
        const nextBtn = document.getElementById("nextBtn");
        const submitBtn = document.getElementById("submitBtn");
        const pageInfo = document.getElementById("pageInfo");
        const thumbnails = document.getElementById("thumbnails");
        let vitriX = 0;
        let vitriY = 0;
        let scalePDF = 1;
        let signatureWidth = urlParams.get("width") || 130;
        let signatureHeight = urlParams.get("height") || 70;
        let imgbase64 = "";
        let pdfDoc = null;
        let currentPage = 1;
        let zoomLevel = 1; // Tỉ lệ zoom mặc định
        let isDragging = false;

          
        var appBaseUrl = '';
        var signingServiceUrl = '';
        var PDFURL = '';
        var IMGSIGNATURE = '';
        function resizeBase64Img(base64, width) {
          
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.src = base64;
                img.onload = function () {
                    const originalSize = { width: img.width, height: img.height };

                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    const aspectRatio = img.height / img.width;
                    const height = width * aspectRatio;

                    canvas.width = width;
                    canvas.height = height;

                    ctx.drawImage(img, 0, 0, width, height);

                    const resizedBase64 = canvas.toDataURL();
                    const newSize = { width, height };

                    resolve({ resizedBase64, originalSize, newSize });
                };
                img.onerror = reject;
            });
        }
        const renderPage = async (pageNum) => {
            const page = await pdfDoc.getPage(pageNum);

            // Lấy viewport với tỉ lệ mặc định
            const viewport = page.getViewport({ scale: 1 });

            // Cố định chiều rộng container
            const containerWidth = pdfContainer.offsetWidth;

            // Tính toán tỉ lệ scale dựa trên chiều rộng container
            let scale = 1.5;
            scalePDF = scale;
            const scaledViewport = page.getViewport({ scale: scale });

            // Điều chỉnh chiều cao container theo tỉ lệ PDF
            //pdfContainer.style.height = `${scaledViewport.height}px`;

            // Cập nhật kích thước canvas
            pdfCanvas.width = scaledViewport.width;
            pdfCanvas.height = scaledViewport.height;

            const context = pdfCanvas.getContext("2d");
            const renderContext = {
                canvasContext: context,
                viewport: scaledViewport
            };

            await page.render(renderContext).promise;

            // Cập nhật thông tin số trang
            pageInfo.textContent = `Trang: ${currentPage} / ${pdfDoc.numPages}`;
            updateSignatureRectSize();
        };

        const updateSignatureRectSize = () => {
            resizeBase64Img(imgbase64, signatureWidth).then(({ resizedBase64, originalSize, newSize }) => {
                console.log("Original Size:", originalSize);
                console.log("New Size:", newSize);
                console.log("Resized Image:", resizedBase64);
                imgbase64 = resizedBase64;
                signatureRect.style.width = `${newSize.width}px`;
                signatureRect.style.height = `${newSize.height}px`;
                signatureRect.style.backgroundImage = `url('${resizedBase64}')`;
            }).catch(error => {
                console.error(error);
            });

            const width = (parseInt(signatureWidth, 10) || 130);
            const height = (parseInt(signatureHeight, 10) || 70);
            if (signatureRect != null) {
                signatureRect.style.width = `${width}px`;
                signatureRect.style.height = `${height}px`;
            }

        };

        const renderThumbnails = async () => {
            for (let i = 1; i <= pdfDoc.numPages; i++) {
                const page = await pdfDoc.getPage(i);
                const viewport = page.getViewport({ scale: 0.2 });

                const canvas = document.createElement("canvas");
                canvas.className = "thumbnail";
                canvas.width = viewport.width;
                canvas.height = viewport.height;

                const context = canvas.getContext("2d");
                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };

                await page.render(renderContext).promise;

                // Thêm sự kiện click để chọn trang
                canvas.addEventListener("click", () => {
                    currentPage = i;
                    renderPage(currentPage);
                    document.querySelectorAll(".thumbnail").forEach((thumb) => thumb.classList.remove("active"));
                    canvas.classList.add("active");
                });

                // Đánh dấu trang đầu tiên
                if (i === 1) {
                    canvas.classList.add("active");
                }

                thumbnails.appendChild(canvas);
            }
        };

        // Load PDF
        var loadPDF = async (url) => {
            pdfDoc = await pdfjsLib.getDocument(url).promise;
            renderPage(currentPage);
            renderThumbnails();
        };

        // Navigation buttons
        prevBtn.addEventListener("click", () => {
            if (currentPage > 1) {
                currentPage--;
                renderPage(currentPage);

                // Đánh dấu thumbnail
                document.querySelectorAll(".thumbnail").forEach((thumb, index) => {
                    thumb.classList.toggle("active", index === currentPage - 1);
                });
            }
        });

        nextBtn.addEventListener("click", () => {
            if (currentPage < pdfDoc.numPages) {
                currentPage++;
                renderPage(currentPage);

                // Đánh dấu thumbnail
                document.querySelectorAll(".thumbnail").forEach((thumb, index) => {
                    thumb.classList.toggle("active", index === currentPage - 1);
                });
            }
        });

        // Add signature rectangle
        addSignatureBtn.addEventListener("click", () => {

            if (!signatureRect) {
                $("#submitBtn").show();
                $("#addSignatureBtn").hide();
                // Tạo vùng chọn chữ ký
                signatureRect = document.createElement("div");
                signatureRect.className = "signatureRect";
                signatureRect.style.position = "absolute";
                signatureRect.style.left = "100px";
                signatureRect.style.top = "100px";

                signatureRect.style.backgroundImage = `url('${imgbase64}')`;

                pdfContainer.appendChild(signatureRect);

                // Biến để theo dõi trạng thái kéo thả
                let offsetX = 0;
                let offsetY = 0;
                let isDragging = false;

                // Hàm bắt tọa độ từ sự kiện chuột hoặc cảm ứng
                const getEventPosition = (e) => {
                    if (e.touches && e.touches.length > 0) {
                        return { x: e.touches[0].clientX, y: e.touches[0].clientY };
                    } else {
                        return { x: e.clientX, y: e.clientY };
                    }
                };

                // Khi nhấn chuột hoặc chạm vào vùng ký
                signatureRect.addEventListener("mousedown", (e) => {
                    isDragging = true;
                    const rect = signatureRect.getBoundingClientRect();
                    offsetX = rect.width / 2;
                    offsetY = rect.height / 2;
                    e.preventDefault();
                });

                signatureRect.addEventListener("touchstart", (e) => {
                    isDragging = true;
                    const rect = signatureRect.getBoundingClientRect();
                    offsetX = rect.width / 2;
                    offsetY = rect.height / 2;
                    e.preventDefault();
                });

                // Khi kéo hoặc di chuyển cảm ứng
                const moveHandler = async (e) => {
                    if (isDragging) {
                        const pos = getEventPosition(e);
                        const container = pdfCanvas.getBoundingClientRect();
                        let left = pos.x - container.left - offsetX;
                        let top = pos.y - container.top - offsetY;

                        left = Math.max(0, Math.min(left, container.width - signatureRect.offsetWidth));
                        top = Math.max(0, Math.min(top, container.height - signatureRect.offsetHeight));
                        vitriX = Math.round(left);
                        vitriY = Math.round(top);

                        signatureRect.style.left = `${left}px`;
                        signatureRect.style.top = `${top}px`;
                    }
                };

                document.addEventListener("mousemove", moveHandler);
                document.addEventListener("touchmove", moveHandler);

                // Khi nhả chuột hoặc kết thúc cảm ứng
                const stopDrag = () => {
                    if (isDragging) {
                        isDragging = false;
                    }
                };

                document.addEventListener("mouseup", stopDrag);
                document.addEventListener("touchend", stopDrag);

                // Hàm cập nhật kích thước vùng ký

                updateSignatureRectSize();
                //signatureWidth.addEventListener("input", updateSignatureRectSize);
                //signatureHeight.addEventListener("input", updateSignatureRectSize);
            }
        });


        document.addEventListener("DOMContentLoaded", () => {
            submitBtn.addEventListener("click", async () => {
                const container = pdfCanvas.getBoundingClientRect();
                const coordinates = {
                    x: Math.round(vitriX),
                    y: Math.round(vitriY),
                    pageNum: currentPage,
                    pdfViewWidth: Math.round(container.width),
                    pdfViewHeight: Math.round(container.height)
                }
                const page = await pdfDoc.getPage(currentPage);
                let viewport = page.getViewport({ scale: 1 });
                var pointA = { x: coordinates.x, y: coordinates.y }
                var pointB = { x: coordinates.x + signatureRect.offsetWidth, y: coordinates.y }
                var pointC = { x: coordinates.x, y: coordinates.y + signatureRect.offsetHeight }
                var pointD = { x: coordinates.x + signatureRect.offsetWidth, y: coordinates.y + signatureRect.offsetHeight }
                // console.log('A',pointA, 'B',pointB, 'C',pointC, 'D',pointD, coordinates.pdfViewWidth, coordinates.pdfViewHeight);

                const rect1 = {
                    A: { x: 0, y: 0 },
                    B: { x: coordinates.pdfViewWidth, y: 0 },
                    C: { x: coordinates.pdfViewWidth, y: coordinates.pdfViewHeight },
                    D: { x: 0, y: coordinates.pdfViewHeight },
                };

                const rect2 = {
                    A: { x: 0, y: viewport.height },
                    B: { x: viewport.width, y: viewport.height },
                    C: { x: viewport.width, y: 0 },
                    D: { x: 0, y: 0 },
                };

                let point = { x: pointA.x + signatureRect.offsetWidth / 2, y: pointA.y + signatureRect.offsetHeight / 2 };
                let newPoint = transformPoint(point, rect1, rect2);
                let offset = { x: newPoint.x - signatureRect.offsetWidth / 2, y: newPoint.y - signatureRect.offsetHeight / 2 };
                let boxsize = { x: offset.x + signatureRect.offsetWidth, y: offset.y + signatureRect.offsetHeight };
                // console.log("Tọa độ mới A:", newPoint, `${Math.round(offset.x)}, ${Math.round(offset.y)}, ${Math.round(boxsize.x)}, ${Math.round(boxsize.y)}`);



                // console.log('base 64', imgbase64)

                let newPointA = transformPoint(pointA, rect1, rect2);
                let newPointB = transformPoint(pointB, rect1, rect2);
                let newPointC = transformPoint(pointC, rect1, rect2);
                let newPointD = transformPoint(pointD, rect1, rect2);
                const rectPoints = [
                    newPointA,
                    newPointB,
                    newPointC,
                    newPointD,
                ];

                const result = findNearestAndFarthestPoints(rectPoints);
                console.log("Điểm gần gốc tọa độ nhất:", result.nearest);
                console.log("Điểm xa gốc tọa độ nhất:", result.farthest);

                console.log("Tọa độ mới A:", newPoint, `${Math.round(result.nearest.x)}, ${Math.round(result.nearest.y)}, ${Math.round(result.farthest.x)}, ${Math.round(result.farthest.y)}`);

                var data = {};
                var temp = { "command": "daky", "data": data };
                data.trangky = currentPage;
                data.offset = result.nearest;
                data.boxsize = result.farthest;
                data.imgbase64 = imgbase64.replace(/^data:image\/\w+;base64,/, '');
                console.log(JSON.stringify(temp));
                window.ReactNativeWebView.postMessage(JSON.stringify(temp));

            });
        });
         // Load the PDF
        // loadPDF("http:localhost/DVCHN.ESign/Files/183883.pdf");;
        //const proxyPDFUrl = 'http://localhost:5037/Home/Proxy?url=' + pdfUrl;
        //const URL = 'http://localhost:5037';
        //const proxyPDFUrl = `${URL}/Home/Proxy?url=${pdfUrl}`;
        function convertToPdfCoordinates(x, y, X, Y, pdfWidth, pdfHeight, signatureWid, signatureHei) {
            // Tính tỷ lệ scale giữa PDF thật và hiển thị
            let scaleX = pdfWidth / X;
            let scaleY = pdfHeight / Y;

            // Chuyển đổi tọa độ
            let pdfX = x * scaleX;
            let pdfY = pdfHeight - (y * scaleY);

            if (pdfY < 0) {
                pdfY = 0;
            } else if (pdfY >= pdfHeight - signatureHei) {
                pdfY = pdfHeight - signatureHei;
            }

            if (pdfX >= pdfWidth - signatureWid) {
                pdfX = pdfWidth - signatureWid;
            }

            return {
                x: pdfX,
                y: pdfY
            };
        }

        function convertToPdfCoordinates2(x, y, X, Y, pdfWidth, pdfHeight) {
            // Tính tỷ lệ scale giữa PDF thật và hiển thị
            let scaleX = pdfWidth / X;
            let scaleY = pdfHeight / Y;

            // Chuyển đổi tọa độ
            let pdfX = x * scaleX;
            let pdfY = pdfHeight - (y * scaleY);

            // if (pdfY < 0) {
            //     pdfY = 0;
            // } else if (pdfY >= pdfHeight) {
            //     pdfY = pdfHeight;
            // }

            // if (pdfX >= pdfWidth) {
            //     pdfX = pdfWidth ;
            // }

            return {
                x: pdfX,
                y: pdfY
            };
        }

        function convertDataURIToBinary(dataURI) {
            var base64Index = dataURI.indexOf(BASE64_MARKER) + BASE64_MARKER.length;
            var base64 = dataURI.substring(base64Index);
            var raw = window.atob(base64);
            var rawLength = raw.length;
            var array = new Uint8Array(new ArrayBuffer(rawLength));

            for (var i = 0; i < rawLength; i++) {
                array[i] = raw.charCodeAt(i);
            }
            return array;
        }
        var BASE64_MARKER = ';base64,';

        window.locadFilePdf = function (pdfbase64, img, widthSign) {
            imgbase64 = img;
            signatureWidth = widthSign;
            loadPDF(convertDataURIToBinary(pdfbase64));
            $(".main").show();
        }
        function vectorSubtract(a, b) {
            return { x: a.x - b.x, y: a.y - b.y };
        }

        function matrixInverse(a, b) {
            const det = a.x * b.y - a.y * b.x;
            if (Math.abs(det) < 1e-6) throw new Error("Singular matrix (rect is degenerate)");
            return [
                { x: b.y / det, y: -a.y / det },
                { x: -b.x / det, y: a.x / det },
            ];
        }

        function matrixMultiply(m, v) {
            return {
                x: m[0].x * v.x + m[1].x * v.y,
                y: m[0].y * v.x + m[1].y * v.y,
            };
        }

        function isValidRect(rect) {
            const AB = vectorSubtract(rect.B, rect.A);
            const AD = vectorSubtract(rect.D, rect.A);
            const BC = vectorSubtract(rect.C, rect.B);
            const DC = vectorSubtract(rect.C, rect.D);

            // Hai cặp vector phải vuông góc (tích vô hướng = 0)
            return Math.abs(AB.x * AD.x + AB.y * AD.y) < 1e-6 &&
                Math.abs(BC.x * DC.x + BC.y * DC.y) < 1e-6;
        }

        function transformPoint(point, rect1, rect2) {
            if (!isValidRect(rect1) || !isValidRect(rect2)) {
                throw new Error("One or both rectangles are not valid.");
            }

            // Vector cơ sở của rect1
            const Vx1 = vectorSubtract(rect1.B, rect1.A);
            const Vy1 = vectorSubtract(rect1.D, rect1.A);

            // Vector cơ sở của rect2
            const Vx2 = vectorSubtract(rect2.B, rect2.A);
            const Vy2 = vectorSubtract(rect2.D, rect2.A);

            // Ma trận chuyển đổi từ rect1 sang rect2
            const invBase1 = matrixInverse(Vx1, Vy1);
            const transformMatrix = [
                matrixMultiply(invBase1, Vx2),
                matrixMultiply(invBase1, Vy2),
            ];

            // Áp dụng phép biến đổi
            const relativePoint = vectorSubtract(point, rect1.A);
            const transformedPoint = matrixMultiply(transformMatrix, relativePoint);

            // Cộng với vector dịch chuyển
            return {
                x: rect2.A.x + transformedPoint.x,
                y: rect2.A.y + transformedPoint.y,
            };
        }
        function findNearestAndFarthestPoints(points) {
            if (points.length !== 4) {
                throw new Error("Phải cung cấp chính xác 4 điểm.");
            }

            // Tính khoảng cách từ mỗi điểm đến (0,0)
            const distance = (point) => Math.sqrt(point.x ** 2 + point.y ** 2);

            let nearest = points[0];
            let farthest = points[0];

            for (let i = 1; i < points.length; i++) {
                if (distance(points[i]) < distance(nearest)) {
                    nearest = points[i];
                }
                if (distance(points[i]) > distance(farthest)) {
                    farthest = points[i];
                }
            }
            nearest.x = Math.round(nearest.x);
            nearest.y = Math.round(nearest.y);
            farthest.x = Math.round(farthest.x);
            farthest.y = Math.round(farthest.y);
            return { nearest, farthest };
        }
        $(function () {
            $("#submitBtn").hide();
            //test();
            window.ReactNativeWebView.postMessage('{\"command\":\"onload\"}');

        });

    </script>
    
</body>

</html>
