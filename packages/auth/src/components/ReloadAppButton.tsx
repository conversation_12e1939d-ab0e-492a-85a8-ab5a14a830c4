import React from 'react';
import RNRestart from 'react-native-restart';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {IconButton, useTheme} from 'react-native-paper';

interface ReloadAppButtonProps {
  label?: string;
  onReloadStart?: () => void;
  onReloadComplete?: () => void;
  buttonStyle?: string;
  labelStyle?: string;
}

export const ReloadAppButton: React.FC<ReloadAppButtonProps> = ({
  onReloadStart,
  onReloadComplete,
}) => {
  const theme = useTheme();

  const handleReload = async () => {
    try {
      onReloadStart?.();

      const keys = await AsyncStorage.getAllKeys();
      for (const key of keys) {
        if (key.includes('Repack')) {
          await AsyncStorage.removeItem(key);
        }
      }

      onReloadComplete?.();
      RNRestart.restart();
    } catch (error) {
      console.error('Error reloading app:', error);
    }
  };

  return (
    <IconButton
      mode="contained-tonal"
      onPress={handleReload}
      style={{
        backgroundColor: theme.colors.surfaceVariant,
      }}
      iconColor={theme.colors.onSurfaceVariant}
      icon={'reload'}
    />
  );
};
