const fs = require('fs');
const path = require('path');
const COPY_PATHS = [
  {
    source: 'src/api',
    target: 'src/api',
  },
  {
    source: 'src/assets/inlineAssets',
    target: 'src/assets/inlineAssets',
  },
  {
    source: 'src/components',
    target: 'src/components',
  },
];

class ClientFileManager {
  constructor(client) {
    this.client = client;
    this.baseDir = path.join(__dirname, '..');
  }

  getSourcePath(basePath) {
    return path.join(this.baseDir, basePath, this.client);
  }

  getTargetPath(basePath) {
    return path.join(this.baseDir, basePath);
  }

  copyFile(sourcePath, targetPath) {
    try {
      // If target file exists, remove it first
      if (fs.existsSync(targetPath)) {
        fs.unlinkSync(targetPath);
      }
      fs.copyFileSync(sourcePath, targetPath);
      return true;
    } catch (error) {
      console.error(
        `Error copying file from ${sourcePath} to ${targetPath}:`,
        error.message,
      );
      return false;
    }
  }

  copyPath(pathConfig) {
    const sourceDir = this.getSourcePath(pathConfig.source);
    const targetDir = this.getTargetPath(pathConfig.target);

    if (!fs.existsSync(sourceDir)) {
      console.log(
        `Skipping ${pathConfig.source}: Source directory doesn't exist: ${sourceDir}`,
      );
      return;
    }

    if (!fs.existsSync(targetDir)) {
      console.log(`Creating target directory: ${targetDir}`);
      fs.mkdirSync(targetDir, {recursive: true});
    }

    try {
      const files = fs.readdirSync(sourceDir);
      console.log(`\nProcessing ${pathConfig.source} directory:`);

      let successCount = 0;
      files.forEach(file => {
        const sourcePath = path.join(sourceDir, file);
        const targetPath = path.join(targetDir, file);

        if (this.copyFile(sourcePath, targetPath)) {
          console.log(`  ✓ Copied ${file}`);
          successCount++;
        }
      });

      console.log(
        `Completed copying ${successCount}/${files.length} files in ${pathConfig.source}`,
      );
    } catch (error) {
      console.error(
        `Error processing ${pathConfig.source} directory:`,
        error.message,
      );
    }
  }

  copyAllPaths() {
    console.log(`Starting file copy process for client: ${this.client}`);
    COPY_PATHS.forEach(pathConfig => this.copyPath(pathConfig));
    console.log('\nFile copy process completed!');
  }
}

const validateClient = client => {
  const validClients = ['HN', 'QN'];
  if (!client) {
    console.error('Please specify client (HN or QN)');
    return false;
  }
  if (!validClients.includes(client)) {
    console.error(
      `Invalid client. Available clients: ${validClients.join(', ')}`,
    );
    return false;
  }
  return true;
};

const main = () => {
  const client = process.argv[2];

  if (!validateClient(client)) {
    process.exit(1);
  }

  const fileManager = new ClientFileManager(client);
  fileManager.copyAllPaths();
};

main();
