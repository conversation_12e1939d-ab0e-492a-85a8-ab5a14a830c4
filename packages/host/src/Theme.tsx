import {DefaultTheme, MD3DarkTheme} from 'react-native-paper';

export const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: 'rgb(192, 0, 20)',
    onPrimary: 'rgb(255, 255, 255)',
    primaryContainer: 'rgb(255, 218, 214)',
    onPrimaryContainer: 'rgb(65, 0, 2)',
    secondary: 'rgb(109, 94, 0)',
    onSecondary: 'rgb(255, 255, 255)',
    secondaryContainer: 'rgb(252, 227, 101)',
    onSecondaryContainer: 'rgb(33, 27, 0)',
    tertiary: 'rgb(16, 109, 32)',
    onTertiary: 'rgb(255, 255, 255)',
    tertiaryContainer: 'rgb(157, 248, 152)',
    onTertiaryContainer: 'rgb(0, 34, 4)',
    error: '#FF5630',
    onSurface: '#1c1b1d',
    surface: '#FFFFFF',
    background: '#FFFBEE',
    onBackground: '#1c1b1d',
    myOwnColor: '#BADA55',
    surfaceVariant: '#FFFFFF',
    onSurfaceVariant: '#23272A',
    outline: '#E5E7EB',
    elevation: {
      level0: 'transparent',
      level1: '#FFFFFF',
      level2: '#F9FAFB',
      level3: '#F3F4F6',
      level4: '#E5E7EB',
      level5: '#D1D5DB',
    },
  } as any,
  roundness: 2,
  isV3: false,
} as any;

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: 'rgb(255, 180, 171)',
    onPrimary: 'rgb(105, 0, 6)',
    primaryContainer: 'rgb(147, 0, 13)',
    onPrimaryContainer: 'rgb(255, 218, 214)',
    secondary: 'rgb(222, 199, 76)',
    onSecondary: 'rgb(57, 48, 0)',
    secondaryContainer: 'rgb(82, 70, 0)',
    onSecondaryContainer: 'rgb(252, 227, 101)',
    tertiary: 'rgb(130, 219, 126)',
    onTertiary: 'rgb(0, 57, 10)',
    tertiaryContainer: 'rgb(0, 83, 18)',
    error: '#FF5630',
    onSurface: '#f5f5f5',
    surface: '#2D3748',
    background: '#23272A',
    onBackground: '#f5f5f5',
    myOwnColor: '#BADA55',
    surfaceVariant: '#2D3748',
    onSurfaceVariant: '#F4F6F8',
    outline: '#4A5568',
    elevation: {
      level0: 'transparent',
      level1: '#2D3748',
      level2: '#374151',
      level3: '#4B5563',
      level4: '#6B7280',
      level5: '#9CA3AF',
    },
  } as any,
  roundness: 2,
  isV3: false,
} as any;
