import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import TabsNavigator from './TabsNavigator';
import DashboardMiniAppScreen from '../screens/DashboardMiniAppScreen';

export type MainStackParamList = {
  Home: undefined;
};

const Main = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  return (
    <Main.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Main.Screen name="Home" component={DashboardMiniAppScreen} />
      {/* <Main.Screen name="Home" component={TabsNavigator} /> */}
    </Main.Navigator>
  );
};

export default MainNavigator;
