const fs = require('fs');
const path = require('path');

// Get command line arguments
const args = process.env.BUILD_ENV
  ? [process.env.BUILD_ENV]
  : process.argv.slice(2);

if (args.length === 0) {
  console.error('Please provide the environment name as an argument (HN or QN)');
  process.exit(1);
}

const environmentName = args[0];

// Validate environment name
if (!['HN', 'QN'].includes(environmentName)) {
  console.error('Environment must be either HN or QN');
  process.exit(1);
}

// Define source and target directories
const sourceDir = path.join(__dirname, 'envSkeleton', environmentName);
const targetDir = __dirname;

// Function to recursively copy files from source to target
function copyFiles(source, target) {
  const files = fs.readdirSync(source);

  files.forEach((file) => {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);

    if (fs.statSync(sourcePath).isDirectory()) {
      if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath);
        console.log(`Created directory: ${file}`);
      }
      copyFiles(sourcePath, targetPath);
    } else {
      if (fs.existsSync(targetPath) && fs.statSync(targetPath).isFile()) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`Replaced: ${file}`);
      } else {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`Added: ${file}`);
      }
    }
  });
}

// Run the copyFiles function
copyFiles(sourceDir, targetDir);