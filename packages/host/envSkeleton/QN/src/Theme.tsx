import {MD3Theme, MD3Colors, DefaultTheme} from 'react-native-paper';
type ACColors = {
  myOwnColor: string;
};
export const appTheme: MD3Theme & {colors: typeof MD3Colors & ACColors} = {
  ...DefaultTheme,
  // Specify custom property
  // myOwnProperty: true,
  colors: {
    ...DefaultTheme.colors,
    primary: '#D71920',
    primaryContainer: '#fdd9d6',
    secondary: '#FFAB00',
    secondaryContainer: '#fcddb2',
    tertiary: '#388e3c',
    tertiaryContainer: '#9cf898',
    error: '#FF5630',
    onSurface: '#1c1b1d',
    surface: 'white',
    myOwnColor: '#BADA55',
    surfaceVariant: '#F4F6F8',
  } as any,
};
