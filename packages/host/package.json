{"name": "host", "version": "0.0.1", "private": true, "scripts": {"align-deps": "rnx-align-deps --write", "android": "react-native run-android --no-packager", "android:release": "react-native run-android --no-packager --mode=release", "bundle:android": "pnpm copy:prod && react-native webpack-bundle --platform android --entry-file index.js --dev false", "bundle:HN:android": "pnpm copy:android-client HN && pnpm copy:prod && react-native webpack-bundle --platform android --entry-file index.js --dev false", "bundle:QN:android": "pnpm copy:android-client QN && pnpm copy:prod && react-native webpack-bundle --platform android --entry-file index.js --dev false", "bundle:ios": "pnpm copy:prod && react-native webpack-bundle --platform ios --entry-file index.js --dev false", "check-deps": "rnx-align-deps", "clear-main": "node clearMainAndroid.js", "toenv": "node scripts/env-changer.js", "replace-env": "node envReplacer.js", "copy:android-client": "node scripts/copy-android-client-files.js", "copy:prod": "rm -fr ./index.js && cp ./index.prod.js index.js", "ios:qn": "react-native  run-ios --no-packager --scheme=CongChucQNI", "ios:hn": "react-native  run-ios --no-packager --scheme=CongChucHNO", "lint": "eslint .", "start": "node scripts/start.js", "test": "jest"}, "dependencies": {"@ac-mobile/common": "0.1.0", "@gorhom/bottom-sheet": "^5.1.1", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "1.24.0", "@react-native-community/netinfo": "11.3.1", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-masked-view/masked-view": "0.3.1", "@react-navigation/bottom-tabs": "7.1.3", "@react-navigation/material-top-tabs": "7.1.0", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.1.14", "axios": "0.27.2", "color": "4.2.3", "jwt-decode": "3.1.2", "lottie-react-native": "6.7.2", "moment": "2.30.1", "nativewind": "2.0.11", "react": "18.2.0", "react-native": "0.74.5", "react-native-app-auth": "^7.2.0", "react-native-blob-util": "0.21.2", "react-native-bootsplash": "6.1.3", "react-native-compressor": "1.10.3", "react-native-device-info": "14.0.2", "react-native-document-picker": "9.3.1", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.21.2", "react-native-image-crop-picker": "0.41.6", "react-native-modal": "13.0.1", "react-native-pager-view": "6.3.0", "react-native-paper": "5.12.5", "react-native-pdf": "6.7.7", "react-native-permissions": "5.2.1", "react-native-paper-dates": "^0.22.34", "react-native-raw-bottom-sheet": "3.0.0", "react-native-reanimated": "3.16.6", "react-native-restart": "0.0.27", "react-native-safe-area-context": "5.1.0", "react-native-screens": "4.3.0", "react-native-svg": "14.1.0", "react-native-tab-view": "4.0.5", "react-native-toast-message": "2.2.1", "react-native-vector-icons": "10.2.0", "react-native-webview": "13.12.5", "react-native-wheel-scrollview-picker": "2.0.6", "react-query": "^3.39.3", "semver": "7.6.3"}, "devDependencies": {"@ac-mobile/sdk": "0.0.18", "@babel/core": "7.25.9", "@babel/preset-env": "7.25.9", "@babel/runtime": "7.25.9", "@callstack/repack": "^4.3.3", "@react-native-community/cli": "13.6.9", "@react-native-community/cli-platform-android": "13.6.9", "@react-native/babel-preset": "0.74.87", "@react-native/eslint-config": "0.74.87", "@react-native/gradle-plugin": "0.74.87", "@react-native/metro-config": "0.74.87", "@react-native/typescript-config": "0.74.87", "@rnx-kit/align-deps": "3.0.1", "@tsconfig/react-native": "^2.0.3", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "@types/semver": "7.5.8", "babel-jest": "^29.6.3", "babel-loader": "^9.2.1", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "tailwindcss": "3.3.2", "terser-webpack-plugin": "^5.3.10", "typescript": "5.0.4", "webpack": "^5.95.0"}, "engines": {"node": ">=18"}, "federatedDependencies": [{"name": "auth", "type": "internal", "url": "https://dl-storage.uat.lokify.xplat.online/ihan<PERSON>-staff"}, {"name": "dashboard", "type": "internal", "url": "https://dl-storage.uat.lokify.xplat.online/ihan<PERSON>-staff"}], "rnx-kit": {"kitType": "app", "alignDeps": {"presets": ["./node_modules/@ac-mobile/sdk/preset"], "requirements": ["@ac-mobile/sdk@0.0.15"], "capabilities": ["super-app"]}}}