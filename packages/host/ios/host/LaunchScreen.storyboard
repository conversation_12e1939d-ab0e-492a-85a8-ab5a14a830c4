<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0"
  toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none"
  useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES"
  colorMatched="YES" initialViewController="01J-lp-oVM">
  <device id="retina4_7" orientation="portrait" appearance="light" />
  <dependencies>
    <deployment identifier="iOS" />
    <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685" />
    <capability name="Safe area layout guides" minToolsVersion="9.0" />
    <capability name="System colors in document resources" minToolsVersion="11.0" />
    <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0" />
  </dependencies>
  <scenes>
    <!--View
    Controller-->
    <scene sceneID="EHf-IW-A2E">
      <objects>
        <viewController id="01J-lp-oVM" sceneMemberID="viewController">
          <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667" />
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES" />
            <subviews>
              <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left"
                horizontalHuggingPriority="251" verticalHuggingPriority="251"
                text="Dành cho công chức" textAlignment="center" lineBreakMode="tailTruncation"
                baselineAdjustment="alignBaselines" minimumFontSize="9"
                translatesAutoresizingMaskIntoConstraints="NO" id="MN2-I3-ftu">
                <rect key="frame" x="0.0" y="626" width="375" height="21" />
                <fontDescription key="fontDescription" type="system" pointSize="17" />
                <nil key="highlightedColor" />
              </label>
              <imageView clipsSubviews="YES" userInteractionEnabled="NO"
                contentMode="scaleAspectFit" horizontalHuggingPriority="251"
                verticalHuggingPriority="251" image="BootSplashLogo-cfd5b9"
                translatesAutoresizingMaskIntoConstraints="NO" id="wCo-fM-CHP">
                <rect key="frame" x="37.5" y="183.5" width="300" height="300" />
                <constraints>
                  <constraint firstAttribute="width" constant="300" id="abc-12-xyz" />
                  <constraint firstAttribute="height" constant="300" id="def-34-uvw" />
                </constraints>
              </imageView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS" />
            <color key="backgroundColor" systemColor="systemBackgroundColor" />
            <constraints>
              <constraint firstItem="Bcu-3y-fUS" firstAttribute="bottom" secondItem="MN2-I3-ftu"
                secondAttribute="bottom" constant="20" id="OZV-Vh-mqD" />
              <constraint firstItem="MN2-I3-ftu" firstAttribute="centerX" secondItem="Bcu-3y-fUS"
                secondAttribute="centerX" id="akx-eg-2ui" />
              <constraint firstItem="MN2-I3-ftu" firstAttribute="leading" secondItem="Bcu-3y-fUS"
                secondAttribute="leading" id="i1E-0Y-4RG" />
              <constraint firstItem="wCo-fM-CHP" firstAttribute="centerX" secondItem="Ze5-6b-2t3"
                secondAttribute="centerX" id="ghi-56-rst" />
              <constraint firstItem="wCo-fM-CHP" firstAttribute="centerY" secondItem="Ze5-6b-2t3"
                secondAttribute="centerY" id="jkl-78-mno" />
            </constraints>
          </view>
        </viewController>
        <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1"
          userLabel="First Responder" sceneMemberID="firstResponder" />
      </objects>
      <point key="canvasLocation" x="52.173913043478265" y="375" />
    </scene>
  </scenes>
  <resources>
    <image name="BootSplashLogo-cfd5b9" width="300" height="300" />
    <systemColor name="systemBackgroundColor">
      <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace" />
    </systemColor>
  </resources>
</document>