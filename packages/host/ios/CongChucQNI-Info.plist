<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleDisplayName</key>
    <string>GQTTHC QN</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(MARKETING_VERSION)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>100</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false />
    <key>LSApplicationQueriesSchemes</key>
    <array>
      <string>itms-apps</string>
    </array>
    <key>LSRequiresIPhoneOS</key>
    <true />
    <key>NSAppTransportSecurity</key>
    <dict>
      <key>NSAllowsArbitraryLoads</key>
      <false />
      <key>NSAllowsLocalNetworking</key>
      <true />
      <key>NSExceptionDomains</key>
      <dict>
        <key>localhost</key>
        <dict>
          <key>NSExceptionAllowsInsecureHTTPLoads</key>
          <true />
        </dict>
      </dict>
    </dict>
    <key>NSDocumentUsageDescription</key>
    <string>$(PRODUCT_NAME) cần truy cập tài liệu để chọn tệp PDF.</string>
    <key>NSFileProviderUsageDescription</key>
    <string>$(PRODUCT_NAME) cần truy cập tài liệu để chọn tệp.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string></string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>$(PRODUCT_NAME) cần quyền để lưu tệp vào thư viện ảnh.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>$(PRODUCT_NAME) cần truy cập thư viện ảnh để chọn tệp PDF.</string>
    <key>NSCameraUsageDescription</key>
    <string>$(PRODUCT_NAME) cần quyền truy cập camera để chụp ảnh và quay video.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>$(PRODUCT_NAME) cần quyền truy cập microphone để quay video có âm thanh.</string>
    <key>RepackPublicKey</key>
    <string>-----BEGIN PUBLIC KEY-----
      MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA1yFp6rg5JqW+ZOWGVVi7
      VkgKOWqQswJIUk1ZnQtrX4JrmG2gZYS19lCbXz9R7pHNy7WcLliIH/upYHCoEOlh
      0tKhaTEtvOkUGY+fGw5LSQWTJaCVUWYWW2Fer3pDsLakMg/bslqs2Mn95ig0ofG7
      utR/n1v7Vr0g7rdlvacZygNYu7FpZrq0OgtNKWUruEo6Tn80dT9sySmGd9NnTv7g
      2D3u44HvXG9AwKJ2+QOFi0QA+QhpNk1iF9Tg5P7//RpQ0lPrl6PhCFpnYTzokh9B
      +EoX7zPUp21ZHpo7vPjkJKixB5g3vJilrNkl84AXU8kX42t+TjcjK2xO1BRkrGdU
      iuebAD8QG4jgfL5pXqubR+/9P5oVDMBPwO0AcctK45w+ltmnCxGVvOZWwi+6PdbY
      fgH7vJliKZpA5LbSpb8wR3Iz2/G3mFVHaB0zX/sjMMTHa0VoVMiMatZdcJGbChN1
      PFsU46kp5FWgAkgO4c/LCKI+7AbZeLK32UP1WGkCKm/ASXHkDVL4X4JwAoVSEXEj
      3ZQK+AOnLjtbCUSK+9Yj8rgqGAWK5cps+g3eH3ZoTm5z5v6RbFDN0VjE4cOa6R30
      QvCJoUHMo210xXFLclS/0lHUpToVoRn5ynSJfvFIBrXsyaSFBr/jrjP5wj65ZmTM
      WrbddPVXyVTlFwHiX/RSqwsCAwEAAQ== -----END PUBLIC KEY-----</string>
    <key>UIAppFonts</key>
    <array>
      <string>AntDesign.ttf</string>
      <string>Entypo.ttf</string>
      <string>EvilIcons.ttf</string>
      <string>Feather.ttf</string>
      <string>FontAwesome.ttf</string>
      <string>FontAwesome5_Brands.ttf</string>
      <string>FontAwesome5_Regular.ttf</string>
      <string>FontAwesome5_Solid.ttf</string>
      <string>Foundation.ttf</string>
      <string>Ionicons.ttf</string>
      <string>MaterialIcons.ttf</string>
      <string>MaterialCommunityIcons.ttf</string>
      <string>SimpleLineIcons.ttf</string>
      <string>Octicons.ttf</string>
      <string>Zocial.ttf</string>
      <string>Fontisto.ttf</string>
    </array>
    <key>UIBackgroundModes</key>
    <array>
      <string>remote-notification</string>
    </array>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
      <string>arm64</string>
    </array>
    <key>UIRequiresFullScreen</key>
    <true />
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false />
  </dict>
</plist>
