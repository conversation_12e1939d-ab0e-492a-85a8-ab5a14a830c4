react_native_path = nil  # ✅ Add this at the top

# Must load before using any React Native pod helpers
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported

prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

# ✅ Helper function for each app target (no test targets)
def setup_app_target(target_name)
  target target_name do
    config = use_native_modules!
    flags = get_default_flags()
    $react_native_path = config[:reactNativePath]

    use_react_native!(
      :path => config[:reactNativePath],
      :hermes_enabled => flags[:hermes_enabled],
      :fabric_enabled => flags[:fabric_enabled],
      :app_path => "#{Pod::Config.instance.installation_root}/.."
    )

    pod 'GoogleUtilities', :modular_headers => true
    pod 'FirebaseCore', :modular_headers => true
  end
end

# ✅ Declare both app targets
setup_app_target('CongChuc')
setup_app_target('CongChucQNI')

# ✅ Required post-install hook
post_install do |installer|
  react_native_post_install(
    installer,
    $react_native_path,
  )
end
