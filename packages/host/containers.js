import { params } from './miniapp_config_param';
import { Platform } from 'react-native';
import { name as appName } from './app.json';
import { version as appVersion } from './package.json';
import { getContainers } from '@ac-mobile/common';

export const getAppContainers = async (environment) => {
  if (environment !== 'local') {

    const finalParams = {
      mainAppName: params.mainAppName,
      hostname: params.hostname,
      version: appVersion,
      platform: Platform.OS,
      appName,
      environment,
    };

    return await getContainers(finalParams);
  } else return { "auth": "http://localhost:9003/[name][ext]", "dashboard": "http://localhost:9002/[name][ext]" }
};
