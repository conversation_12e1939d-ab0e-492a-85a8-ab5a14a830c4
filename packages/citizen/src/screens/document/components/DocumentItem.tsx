import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import type {DocumentItem} from '../../../requester-biz-service/apis/documents-api';
import {formatFileSize} from '../utils';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from 'react-native-paper';

import {
  AUDIOFileMultiColor,
  IMGFileMultiColor,
  PDFFileMultiColor,
  VIDEOFileMultiColor,
} from '../../../icons';
import SvgDocumentDownload from '../../../icons/cong-dan_icons/DocumentDownload';
import SvgDocumentUpload from '../../../icons/cong-dan_icons/DocumentUpload';

interface DocumentItemProps {
  item: DocumentItem;
  onPress: (item: DocumentItem) => void;
  onCheckboxPress?: (item: DocumentItem) => void;
}

const DocumentItemComponent: React.FC<DocumentItemProps> = ({
  item,
  onPress,
  onCheckboxPress,
}) => {
  const theme = useTheme();
  const styles = React.useMemo(
    () =>
      StyleSheet.create({
        container: {
          flexDirection: 'row',
          backgroundColor: theme.colors.surface,
          padding: 12,
          borderRadius: 8,
          alignItems: 'flex-start',
          marginBottom: 8,
          shadowColor: theme.dark ? '#000' : theme.colors.outline,
          shadowOffset: {width: 0, height: 1},
          shadowOpacity: 0.1,
          shadowRadius: 2,
          elevation: 2,
        },
        checkbox: {
          padding: 8,
          marginRight: 4,
        },
        iconContainer: {
          width: 40,
          height: 40,
          justifyContent: 'center',
          alignItems: 'center',
          marginRight: 12,
        },
        content: {
          flex: 1,
          gap: 4,
        },
        title: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.onSurface,
          lineHeight: 24,
          marginBottom: 4,
        },
        metaContainer: {
          flexDirection: 'row',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 8,
        },
        metaText: {
          fontSize: 14,
          color: theme.colors.onSurfaceVariant,
          lineHeight: 22,
        },
        chipContainer: {
          flexDirection: 'row',
          gap: 8,
        },
        chip: {
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 2,
          paddingHorizontal: 6,
          borderRadius: 8,
        },
        chipIcon: {
          marginRight: 4,
        },
        chipText: {
          fontSize: 13,
          fontWeight: '500',
          lineHeight: 18,
        },
      }),
    [theme],
  );

  const renderDocumentIcon = (contentType: string) => {
    if (contentType?.includes('pdf')) {
      return <PDFFileMultiColor size={40} />;
    } else if (contentType?.includes('video')) {
      return <VIDEOFileMultiColor size={40} />;
    } else if (contentType?.includes('audio')) {
      return <AUDIOFileMultiColor size={40} />;
    } else if (contentType?.includes('image')) {
      return <IMGFileMultiColor size={40} />;
    }
    return (
      <Icon
        name="file-document"
        size={40}
        color={theme.colors.onSurfaceVariant}
      />
    );
  };

  const renderDocumentIconByType = (type: string) => {
    if (type === 'owner') {
      return <SvgDocumentDownload />;
    } else if (type === 'issuer') {
      return <SvgDocumentUpload />;
    }
    return (
      <Icon
        name="file-document"
        size={40}
        color={theme.colors.onSurfaceVariant}
      />
    );
  };

  const renderChip = (text: string, color: string, iconName: string) => (
    <View
      style={[
        styles.chip,
        {
          backgroundColor:
            color === '#22c55e'
              ? theme.colors.secondaryContainer
              : theme.colors.primaryContainer,
        },
      ]}>
      <Icon name={iconName} size={18} color={color} style={styles.chipIcon} />
      <Text style={[styles.chipText, {color}]}>{text}</Text>
    </View>
  );
  return (
    <TouchableOpacity style={styles.container} onPress={() => onPress(item)}>
      {onCheckboxPress && (
        <TouchableOpacity
          style={styles.checkbox}
          onPress={() => onCheckboxPress(item)}>
          <Icon
            name="checkbox-blank-outline"
            size={24}
            color={theme.colors.onSurfaceVariant}
          />
        </TouchableOpacity>
      )}

      <View style={styles.iconContainer}>
        {/* {renderDocumentIcon(item.contentType)} */}

        {renderDocumentIconByType(
          item?.issuerId === item?.ownerId ? 'owner' : 'issuer',
        )}
      </View>

      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={2}>
          {item.name}
        </Text>

        <View style={styles.metaContainer}>
          <Text style={styles.metaText}>{formatFileSize(item.size || 0)}</Text>

          <View style={styles.chipContainer}>
            {item.isSigned &&
              renderChip('Ký số', theme.colors.secondary, 'check-decagram')}
            {item.metadata?.isAssigned &&
              renderChip('Gán hồ sơ', theme.colors.primary, 'folder')}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default DocumentItemComponent;
