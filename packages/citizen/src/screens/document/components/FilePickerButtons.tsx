import React, {useCallback, useEffect, useState} from 'react';
import {View, StyleSheet, Platform, Alert} from 'react-native';
import {Button} from 'react-native-paper';
import ImagePicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import DocumentPicker from 'react-native-document-picker';
import {PermissionsAndroid} from 'react-native';
import type {FilePickerButtonsProps, PickedFile} from '../types';

export const FilePickerButtons: React.FC<FilePickerButtonsProps> = props => {
  const {
    isImageMode,
    onFilesPicked,
    showPickerMenu,
    autoPickType,
    selectedFiles,
  } = props;

  const isVideoMode =
    autoPickType === 'video' || autoPickType === 'videoCamera';
  const isDocumentMode = autoPickType === 'pdf' || autoPickType === 'document';
  const hasSelectedVideo = selectedFiles.some((file: PickedFile) =>
    file.type.startsWith('video/'),
  );
  const hasSelectedDocument = selectedFiles.some(
    (file: PickedFile) =>
      file.type.includes('pdf') ||
      file.type.includes('document') ||
      file.type.includes('application/'),
  );

  const handleCameraCapture = useCallback(async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert(
        'Quyền truy cập camera',
        'Cần quyền truy cập camera để chụp ảnh.',
      );
      return;
    }
    ImagePicker.openCamera({
      width: 1920,
      height: 1080,
      cropping: false,
      mediaType: 'photo',
      quality: 0.8,
      includeBase64: false,
      includeExif: false,
    })
      .then((image: ImageOrVideo) => {
        const newFile = {
          uri: image.path,
          name: `camera_${Date.now()}.jpg`,
          type: image.mime || 'image/jpeg',
          size: image.size || 0,
        };
        onFilesPicked([newFile]);
      })
      .catch(error => {
        if (error.code === 'E_PERMISSION_MISSING') {
          Alert.alert(
            'Quyền truy cập camera',
            'Ứng dụng cần quyền truy cập camera để chụp ảnh. Vui lòng cấp quyền trong cài đặt.',
          );
        } else if (error.code !== 'E_PICKER_CANCELLED') {
          Alert.alert('Lỗi', 'Không thể chụp ảnh. Vui lòng thử lại.');
        }
      });
  }, [onFilesPicked]);

  const handleVideoCameraCapture = useCallback(async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert(
        'Quyền truy cập camera',
        'Cần quyền truy cập camera để quay video.',
      );
      return;
    }
    ImagePicker.openCamera({
      mediaType: 'video',
      quality: 'high',
      includeBase64: false,
      includeExif: false,
    })
      .then((video: ImageOrVideo) => {
        const fileName = `video_${Date.now()}.mp4`;
        /**
         * TODO: Check video size (max 120MB)
         */
        if (video && video.size! > 120 * 1024 * 1024) {
          Alert.alert(
            'Lỗi',
            'Video vượt quá kích thước tối đa cho phép (120MB).',
          );
          return;
        }
        onFilesPicked([
          {
            uri: video.path,
            name: fileName,
            type: video.mime || 'video/mp4',
            size: video.size || 0,
          },
        ]);
      })
      .catch(error => {
        if (error.code === 'E_PERMISSION_MISSING') {
          Alert.alert(
            'Quyền truy cập camera',
            'Ứng dụng cần quyền truy cập camera để quay video. Vui lòng cấp quyền trong cài đặt.',
          );
        } else if (error.code !== 'E_PICKER_CANCELLED') {
          Alert.alert('Lỗi', 'Không thể quay video. Vui lòng thử lại.');
        }
      });
  }, [onFilesPicked]);

  const handleImagePicker = useCallback(() => {
    ImagePicker.openPicker({
      width: 1920,
      height: 1080,
      cropping: false,
      mediaType: 'photo',
      quality: 0.8,
      multiple: isImageMode,
      includeBase64: false,
      includeExif: false,
    })
      .then((images: ImageOrVideo | ImageOrVideo[]) => {
        const imageArray = Array.isArray(images) ? images : [images];
        const newFiles = imageArray.map((image, index) => {
          const fileName = image.filename || `image_${Date.now()}_${index}.jpg`;
          return {
            uri: image.path,
            name: fileName,
            type: image.mime || 'image/jpeg',
            size: image.size || 0,
          };
        });
        onFilesPicked(newFiles);
      })
      .catch(error => {
        if (error.code === 'E_PERMISSION_MISSING') {
          Alert.alert(
            'Quyền truy cập thư viện ảnh',
            'Ứng dụng cần quyền truy cập thư viện ảnh. Vui lòng cấp quyền trong cài đặt.',
          );
        } else if (error.code !== 'E_PICKER_CANCELLED') {
          Alert.alert('Lỗi', 'Không thể chọn ảnh. Vui lòng thử lại.');
        }
      });
  }, [isImageMode, onFilesPicked]);

  const handleVideoPicker = useCallback(() => {
    ImagePicker.openPicker({
      mediaType: 'video',
      quality: 'high',
      multiple: false,
    })
      .then((video: ImageOrVideo) => {
        const fileName = video.filename || `video_${Date.now()}.mp4`;
        /**
         * TODO: Check video size (max 120MB)
         */
        if (video && video.size! > 120 * 1024 * 1024) {
          Alert.alert(
            'Lỗi',
            'Video vượt quá kích thước tối đa cho phép (120MB).',
          );
          return;
        }
        onFilesPicked([
          {
            uri: video.path,
            name: fileName,
            type: video.mime || 'video/mp4',
            size: video.size || 0,
          },
        ]);
      })
      .catch(error => {
        if (error.code !== 'E_PICKER_CANCELLED') {
          Alert.alert('Lỗi', 'Không thể chọn video. Vui lòng thử lại.');
        }
      });
  }, [onFilesPicked]);

  const handleDocumentPicker = useCallback(() => {
    console.log('Opening document picker...');
    DocumentPicker.pickSingle({
      type: [DocumentPicker.types.pdf],
      allowMultiSelection: false,
      copyTo: 'cachesDirectory',
      presentationStyle: 'overFullScreen',
    })
      .then(async doc => {
        if (!doc.fileCopyUri) {
          Alert.alert('Lỗi', 'Không thể truy cập file đã chọn.');
          return;
        }
        /**
         * TODO: Check file size (max 120MB)
         */
        if (doc && doc.size! > 120 * 1024 * 1024) {
          Alert.alert(
            'Lỗi',
            'File vượt quá kích thước tối đa cho phép (120MB).',
          );
          return;
        }

        // Use rn-fetch-blob to read as base64 and print out
        try {
          // const filePath = decodeURIComponent(
          //   doc.fileCopyUri.replace('file://', ''),
          // );
          // const base64 = await RNFetchBlob.fs.readFile(filePath, 'base64');
          // console.log('PDF base64:', base64);
        } catch (err) {
          console.error('Error reading PDF as base64 with rn-fetch-blob:', err);
        }
        onFilesPicked([
          {
            uri: doc.fileCopyUri || '',
            name: doc.name || `document_${Date.now()}`,
            type: doc.type || 'application/pdf',
            size: doc.size || 0,
          },
        ]);
      })
      .catch(error => {
        if (!DocumentPicker.isCancel(error)) {
          Alert.alert('Lỗi', 'Không thể chọn tài liệu. Vui lòng thử lại.');
        }
      });
  }, [onFilesPicked]);

  useEffect(() => {
    if (autoPickType && !selectedFiles.length) {
      // Only auto-pick if no files are selected
      const timer = setTimeout(() => {
        switch (autoPickType) {
          case 'camera':
            handleCameraCapture();
            break;
          case 'image':
            handleImagePicker();
            break;
          case 'video':
            handleVideoPicker();
            break;
          case 'videoCamera':
            handleVideoCameraCapture();
            break;
          case 'pdf':
          case 'document':
            handleDocumentPicker();
            break;
          default:
            break;
        }
      }, 500); // Small delay to ensure screen is fully loaded
      return () => clearTimeout(timer);
    }
  }, [
    autoPickType,
    handleCameraCapture,
    handleDocumentPicker,
    handleImagePicker,
    handleVideoCameraCapture,
    handleVideoPicker,
    selectedFiles.length, // Add this dependency
  ]);

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Quyền truy cập Camera',
            message:
              'Ứng dụng cần quyền truy cập camera để chụp ảnh và quay video.',
            buttonNeutral: 'Hỏi lại sau',
            buttonNegative: 'Hủy',
            buttonPositive: 'Đồng ý',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const themedStyles = () =>
    StyleSheet.create({
      pickerButtons: {
        marginTop: 16,
        marginBottom: 8,
      },
      pickerButton: {
        marginBottom: 8,
      },
      buttonGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        gap: 8,
      },
      gridButton: {
        flex: 1,
        minWidth: '48%',
        marginBottom: 8,
      },
    });
  const styles = themedStyles();

  return (
    <View style={styles.pickerButtons}>
      {isImageMode ? (
        // In image mode, always allow adding more images
        <Button
          mode="outlined"
          icon="image-plus"
          onPress={handleImagePicker}
          style={styles.pickerButton}>
          Chọn thêm ảnh
        </Button>
      ) : isVideoMode ? (
        // In video mode, only show button if no video is selected
        !hasSelectedVideo && (
          <Button
            mode="outlined"
            icon="video-plus"
            onPress={handleVideoPicker}
            style={styles.pickerButton}>
            Chọn video
          </Button>
        )
      ) : isDocumentMode ? (
        // In document mode, only show button if no document is selected
        !hasSelectedDocument && (
          <Button
            mode="outlined"
            icon="file-document"
            onPress={handleDocumentPicker}
            style={styles.pickerButton}>
            Chọn tài liệu
          </Button>
        )
      ) : showPickerMenu ? (
        // In general mode, show all picker options
        <View style={styles.buttonGrid}>
          <Button
            mode="outlined"
            icon="image-plus"
            onPress={handleImagePicker}
            style={styles.gridButton}>
            Chọn ảnh
          </Button>

          {!hasSelectedVideo && (
            <Button
              mode="outlined"
              icon="video-plus"
              onPress={handleVideoPicker}
              style={styles.gridButton}>
              Chọn video
            </Button>
          )}

          {!hasSelectedDocument && (
            <Button
              mode="outlined"
              icon="file-document"
              onPress={handleDocumentPicker}
              style={styles.gridButton}>
              Chọn tài liệu
            </Button>
          )}
        </View>
      ) : null}
    </View>
  );
};
