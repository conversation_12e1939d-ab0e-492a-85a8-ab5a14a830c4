import {ApiClient, useAuthStore} from '@ac-mobile/common';
import {Platform} from 'react-native';
import RNRestart from 'react-native-restart';
import {ENDPOINT} from '../api/api-config';

const RequesterServiceClient = ApiClient.getInstance('requester-service');

// export const defaultHeaders = {
//   'X-Requester-Service-Id': 'eb5154cb-07d6-4b2c-99e5-f9bd07170d0f',
//   'X-Requester-Service-Key-Auth-Profile': 'citizen',
// };

RequesterServiceClient.setSelectRefreshing(() => {
  return useAuthStore.getState().isRefreshing;
})
  .setSelectAccessToken(() => {
    return useAuthStore.getState().accessToken;
  })
  .setSelectRefreshToken(() => {
    return useAuthStore.getState().refreshToken;
  })
  // .setDefaultHeader(() => {
  //    return {...defaultHeaders};
  // })
  .setOnError(async (status: number) => {
    if (status === 401) {
      try {
        useAuthStore.getState().logout();
      } catch (error) {
        if (Platform.OS !== 'ios') {
          RNRestart.restart();
        }
      }
    }
  });

RequesterServiceClient.setEndpoint(`${ENDPOINT}/requester-biz/api`);

const RequesterApi = RequesterServiceClient.api;

export {RequesterServiceClient, RequesterApi};
