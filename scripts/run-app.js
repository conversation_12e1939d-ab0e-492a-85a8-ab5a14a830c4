const { spawn } = require('child_process');
const path = require('path');

const CLIENTS = {
  HN: {
    name: '<PERSON><PERSON>',
    id: 'hanoi'
  },
  QN: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    id: 'quangninh'
  }
};

const PROJECTS = ['host', 'dashboard', 'auth'];
const PLATFORMS = ['ios', 'android'];
const ENVIRONMENTS = ['production', 'staging', 'uat', 'development', 'local'];

const runApp = async (client, project, platform, environment) => {
  // Validate inputs
  if (!CLIENTS[client]) {
    console.error(`Invalid client: ${client}`);
    console.log('Available clients:', Object.keys(CLIENTS).join(', '));
    process.exit(1);
  }

  if (!PROJECTS.includes(project)) {
    console.error(`Invalid project: ${project}`);
    console.log('Available projects:', PROJECTS.join(', '));
    process.exit(1);
  }

  if (!PLATFORMS.includes(platform)) {
    console.error(`Invalid platform: ${platform}`);
    console.log('Available platforms:', PLATFORMS.join(', '));
    process.exit(1);
  }

  if (!ENVIRONMENTS.includes(environment)) {
    console.error(`Invalid environment: ${environment}`);
    console.log('Available environments:', ENVIRONMENTS.join(', '));
    process.exit(1);
  }

  // Set environment first
  const envChangeProcess = spawn('pnpm', ['--filter', project, 'run', 'toenv', environment], {
    stdio: 'inherit',
    shell: true
  });

  await new Promise((resolve, reject) => {
    envChangeProcess.on('exit', code => {
      if (code === 0) resolve();
      else reject(new Error(`Environment change failed with code ${code}`));
    });
  });

  // Run the app based on platform
  const runCommand = platform === 'ios' ? `ios:${client.toLowerCase()}` : platform;
  const runProcess = spawn('pnpm', ['--filter', project, 'run', runCommand], {
    stdio: 'inherit',
    shell: true
  });

  runProcess.on('error', err => {
    console.error('Failed to run the application:', err);
    process.exit(1);
  });
};

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 4) {
  console.error('Usage: run-app <client> <project> <platform> <environment>');
  console.log('Available clients:', Object.keys(CLIENTS).join(', '));
  console.log('Available projects:', PROJECTS.join(', '));
  console.log('Available platforms:', PLATFORMS.join(', '));
  console.log('Available environments:', ENVIRONMENTS.join(', '));
  process.exit(1);
}

const [client, project, platform, environment] = args;
runApp(client, project, platform, environment);
