const { spawn } = require('child_process');

const CLIENTS = {
  HN: {
    name: '<PERSON><PERSON>',
    id: 'hanoi'
  },
  QN: {
    name: '<PERSON><PERSON><PERSON><PERSON>nh',
    id: 'quangninh'
  }
};

const PACKAGES = {
  auth: {
    port: 9003,
    envs: ['dev', 'uat', 'prod']
  },
  dashboard: {
    port: 9002,
    envs: ['dev', 'uat', 'prod'],
    standalone: true
  },
  host: {
    port: 9000,
    envs: ['production', 'staging', 'uat', 'development', 'local'],
  },
  'catalog-server': {
    port: 9001
  },
};

const startPackage = async (client, packageName, options = {}) => {
  const clientConfig = CLIENTS[client];
  if (!clientConfig) {
    console.error(`Unknown client: ${client}`);
    console.log('Available clients:', Object.keys(CLIENTS).join(', '));
    process.exit(1);
  }

  const packageConfig = PACKAGES[packageName];
  if (!packageConfig) {
    console.error(`Unknown package: ${packageName}`);
    console.log('Available packages:', Object.keys(PACKAGES).join(', '));
    process.exit(1);
  }

  // Set client-specific environment variable
  process.env.CLIENT_ID = clientConfig.id;
  process.env.CLIENT_NAME = clientConfig.name;

  const args = ['--filter', packageName];

  // Handle environment-specific starts
  const startArg = packageName === 'catalog-server' ? 'start' : `start ${client}`;
  if (options.env && packageConfig.envs?.includes(options.env)) {
    args.push(`${startArg} ${options.env}`);
  } else if (options.standalone && packageConfig.standalone) {
    args.push(`${startArg} --standalone`);
  } else {
    args.push(startArg);
  }

  const startProcess = spawn('pnpm', args, {
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      ...(options.standalone ? { STANDALONE: '1' } : {})
    }
  });

  startProcess.on('error', (err) => {
    console.error(`Failed to start ${packageName} for ${clientConfig.name}:`, err);
    process.exit(1);
  });
};

const main = async () => {
  const args = process.argv.slice(2);

  if (args.length < 2) {
    console.error('Usage: start <client> <package> [env] [--standalone]');
    console.log('Available clients:', Object.keys(CLIENTS).join(', '));
    console.log('Available packages:', Object.keys(PACKAGES).join(', '));
    process.exit(1);
  }

  const [client, packageName, env] = args;
  const isStandalone = args.includes('--standalone');

  await startPackage(client, packageName, { env, standalone: isStandalone });
};

main().catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
