{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Development QN Dev Android",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qn-host",
        "start-qn-auth-dev",
        "start-qn-dashboard-dev",
        "start-qn-citizen-dev",
        "adb-reverse",
        "run-android-qn-local",
      ],
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "Start Development QNC Dev Android",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qnc-host",
        "start-qn-auth-dev",
        "start-qn-dashboard-dev",
        "start-qn-citizen-dev",
        "adb-reverse",
        "run-android-qnc-local",
      ],
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "start-catalog",
      "type": "shell",
      "command": "pnpm start:catalog-server",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "run-android-qn-local",
      "type": "shell",
      "command": "pnpm run run-app QN host android local",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "adb-reverse",
      "type": "shell",
      "command": "pnpm adbreverse",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "start-hn-host",
      "type": "shell",
      "command": "pnpm start:hn:host",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qn-auth-dev",
      "type": "shell",
      "command": "pnpm start:qn:auth:dev",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qn-dashboard-dev",
      "type": "shell",
      "command": "pnpm start:qn:dashboard:dev",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qn-citizen-dev",
      "type": "shell",
      "command": "pnpm start:qn:citizen:dev",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qn-host",
      "type": "shell",
      "command": "pnpm start:qn:host",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qnc-host",
      "type": "shell",
      "command": "pnpm start:qnc:host",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "run-android-qnc-local",
      "type": "shell",
      "command": "pnpm run run-app QNC host android local",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "run-ios-qnc-local",
      "type": "shell",
      "command": "pnpm run run-app QNC host ios local",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "Start Development QNC Dev Android",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qnc-host",
        "start-qn-auth-dev",
        "start-qn-dashboard-dev",
        "start-qn-citizen-dev",
        "adb-reverse",
        "run-android-qnc-local",
      ],
      "group": {
        "kind": "build",
      }
    },
    {
      "label": "Start Development QNC Dev Ios",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qnc-host",
        "start-qn-auth-dev",
        "start-qn-dashboard-dev",
        "start-qn-citizen-dev",
        "run-ios-qnc-local",
      ],
      "group": {
        "kind": "build",
      }
    },
    {
      "label": "Start Production QNC Ios",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qnc-host-prod",
        "start-qn-auth-prod",
        "start-qn-dashboard-prod",
        "start-qn-citizen-prod",
        "run-ios-qnc-prod",
      ],
      "group": {
        "kind": "build",
      }
    },
    {
      "label": "start-qnc-host-prod",
      "type": "shell",
      "command": "pnpm copy:prod && pnpm start:qnc:host",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qn-auth-prod",
      "type": "shell",
      "command": "pnpm start:qn:auth:prod",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qn-dashboard-prod",
      "type": "shell",
      "command": "pnpm start:qn:dashboard:prod",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "start-qn-citizen-prod",
      "type": "shell",
      "command": "pnpm start:qn:citizen:prod",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      }
    },
    {
      "label": "run-ios-qnc-prod",
      "type": "shell",
      "command": "pnpm copy:prod && pnpm run run-app QNC host ios production",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "run-android-qnc-prod",
      "type": "shell",
      "command": "pnpm copy:prod && pnpm run run-app QNC host android production",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "Start Development QNC prod Android",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qnc-host-prod",
        "start-qn-auth-prod",
        "start-qn-dashboard-prod",
        "start-qn-citizen-prod",
        "adb-reverse",
        "run-android-qnc-prod",
      ],
      "group": {
        "kind": "build",
      }
    },
    {
      "label": "Start Development QN Product ios",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qn-host",
        "start-qn-auth-prod",
        "start-qn-dashboard-prod",
        "start-qn-citizen-prod",
        "run-ios-qn-prod",
      ],
      "group": {
        "kind": "build",
      }
    },
    {
      "label": "run-ios-qn-prod",
      "type": "shell",
      "command": "pnpm copy:prod && pnpm run run-app QN host ios production",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "runOptions": {
        "runOn": "default"
      }
    },
    {
      "label": "Start Development QNC Product ios",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qnc-host-prod",
        "start-qn-auth-prod",
        "start-qn-dashboard-prod",
        "start-qn-citizen-prod",
        "run-ios-qnc-prod"
      ],
      "group": {
        "kind": "build"
      }
    },
    {
      "label": "Start Development QN Prod Android",
      "dependsOrder": "parallel",
      "dependsOn": [
        "start-catalog",
        "start-qn-host",
        "start-qn-auth-prod",
        "start-qn-dashboard-prod",
        "start-qn-citizen-prod",
        "adb-reverse",
        "run-android-qn-prod"
      ],
      "group": {
        "kind": "build"
      }
    },
  ]
}
